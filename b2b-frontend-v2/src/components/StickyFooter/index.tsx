"use client";

import React from "react";

type StickyFooterPropsI = {
  leftItems?: React.ReactNode[];
  centerItems?: React.ReactNode[];
  rightItems?: React.ReactNode[];
  version?: string;
  showVersion?: boolean;
  className?: string;
};

const StickyFooter: React.FC<StickyFooterPropsI> = ({
  leftItems = [],
  centerItems = [],
  rightItems = [],
  version = "1.0.0",
  showVersion = true,
  className = "",
}) => {
  const defaultLeftItems = showVersion ? [
    <div key="version" className="flex items-center gap-2 text-xs">
      <span className="font-medium">Version:</span>
      <span className="bg-blue-500 px-2 py-0.5 rounded text-white border border-blue-400">{version}</span>
    </div>
  ] : [];

  const allLeftItems = [...defaultLeftItems, ...leftItems];

  return (
    <footer className={`fixed bottom-0 left-0 right-0 z-40 bg-blue-600 border-t border-blue-700 text-white ${className}`}>
      <div className="px-4 py-1">
        <div className="flex items-center justify-between text-xs">
          {/* Left Section */}
          <div className="flex items-center gap-4">
            {allLeftItems.map((item, index) => (
              <div key={index} className="flex items-center">
                {item}
              </div>
            ))}
          </div>

          {/* Center Section */}
          <div className="flex items-center gap-4">
            {centerItems.map((item, index) => (
              <div key={index} className="flex items-center">
                {item}
              </div>
            ))}
          </div>

          {/* Right Section */}
          <div className="flex items-center gap-4">
            {rightItems.map((item, index) => (
              <div key={index} className="flex items-center">
                {item}
              </div>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default StickyFooter;
