"use client";

import React from "react";

type StickyFooterPropsI = {
  leftItems?: React.ReactNode[];
  centerItems?: React.ReactNode[];
  rightItems?: React.ReactNode[];
  version?: string;
  showVersion?: boolean;
  className?: string;
};

const StickyFooter: React.FC<StickyFooterPropsI> = ({
  leftItems = [],
  centerItems = [],
  rightItems = [],
  version = "1.0.0",
  showVersion = true,
  className = "",
}) => {
  const defaultLeftItems = showVersion ? [
    <div key="version" className="flex items-center gap-1 hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors text-xs">
      <span className="text-blue-200">⚡</span>
      <span className="font-medium">v{version}</span>
    </div>
  ] : [];

  const allLeftItems = [...defaultLeftItems, ...leftItems];

  return (
    <footer className={`fixed bottom-0 left-0 right-0 z-40 bg-blue-600 border-t border-blue-500 text-white shadow-lg ${className}`}>
      <div className="px-4 py-1">
        <div className="flex items-center justify-between text-xs font-medium">
          {/* Left Section */}
          <div className="flex items-center gap-2">
            {allLeftItems.map((item, index) => (
              <div key={index} className="flex items-center">
                {item}
              </div>
            ))}
          </div>

          {/* Center Section */}
          <div className="flex items-center gap-2">
            {centerItems.map((item, index) => (
              <div key={index} className="flex items-center">
                {item}
              </div>
            ))}
          </div>

          {/* Right Section */}
          <div className="flex items-center gap-2">
            {rightItems.map((item, index) => (
              <div key={index} className="flex items-center">
                {item}
              </div>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default StickyFooter;
