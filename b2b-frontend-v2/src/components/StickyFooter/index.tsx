"use client";

import React, { useState, useRef, useEffect } from "react";
import { ChevronDownIcon } from "../../../public/assets/svgs/common";

type VersionI = {
  version: string;
  label: string;
  isActive?: boolean;
  releaseDate?: string;
};

type StickyFooterPropsI = {
  version?: string;
  versions?: VersionI[];
  className?: string;
  sidebarCollapsed?: boolean;
  onVersionChange?: (version: string) => void;
};

const StickyFooter: React.FC<StickyFooterPropsI> = ({
  version = "2.1.4",
  versions = [
    { version: "2.1.4", label: "v2.1.4 (Current)", isActive: true, releaseDate: "Dec 15, 2024" },
    { version: "2.1.3", label: "v2.1.3", releaseDate: "Dec 1, 2024" },
    { version: "2.1.2", label: "v2.1.2", releaseDate: "Nov 15, 2024" },
    { version: "2.1.1", label: "v2.1.1", releaseDate: "Nov 1, 2024" },
    { version: "2.1.0", label: "v2.1.0", releaseDate: "Oct 15, 2024" },
    { version: "2.0.9", label: "v2.0.9", releaseDate: "Oct 1, 2024" },
    { version: "2.0.8", label: "v2.0.8", releaseDate: "Sep 15, 2024" },
    { version: "2.0.7", label: "v2.0.7", releaseDate: "Sep 1, 2024" },
  ],
  className = "",
  sidebarCollapsed = true,
  onVersionChange,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleVersionSelect = (selectedVersion: string) => {
    setIsDropdownOpen(false);
    onVersionChange?.(selectedVersion);
  };

  const currentVersion = versions.find(v => v.version === version) || versions[0];

  return (
    <footer
      className={`fixed bottom-0 right-0 z-40 bg-blue-600 border-t border-blue-500 text-white shadow-lg transition-all duration-300 ${
        sidebarCollapsed ? 'left-16' : 'left-64'
      } ${className}`}
    >
      <div className="px-4">
        <div className="flex items-center justify-end">
          {/* Version Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center gap-1 hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors text-xs font-medium"
            >
              <span className="text-blue-200">⚡</span>
              <span>{currentVersion.label}</span>
              <ChevronDownIcon
                className={`w-3 h-3 text-blue-200 transition-transform duration-200 ${
                  isDropdownOpen ? 'rotate-180' : ''
                }`}
              />
            </button>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <div className="absolute bottom-full right-0 mb-2 w-72 bg-white border border-gray-300 rounded-md shadow-xl overflow-hidden z-50">
                <div className="bg-gray-50 px-3 py-2 border-b border-gray-200">
                  <h3 className="text-sm font-semibold text-gray-700">Select Version</h3>
                </div>
                <div className="py-1 max-h-80 overflow-y-auto">
                  {versions.map((versionItem) => (
                    <button
                      key={versionItem.version}
                      onClick={() => handleVersionSelect(versionItem.version)}
                      className={`w-full text-left px-3 py-3 text-sm hover:bg-blue-50 transition-colors border-l-2 ${
                        versionItem.isActive
                          ? 'bg-blue-50 text-blue-700 border-l-blue-500 font-medium'
                          : 'text-gray-700 border-l-transparent hover:border-l-blue-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{versionItem.label}</span>
                            {versionItem.isActive && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Current
                              </span>
                            )}
                          </div>
                          {versionItem.releaseDate && (
                            <div className="text-xs text-gray-500 mt-1">
                               {versionItem.releaseDate}
                            </div>
                          )}
                        </div>
                        {versionItem.isActive && (
                          <div className="flex-shrink-0 ml-2">
                            <span className="text-blue-600 text-lg">●</span>
                          </div>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default StickyFooter;
