"use client";

import React, { useState } from "react";
import {
  SearchIconSimple,
  ChatIcon,
  NotificationIcon,
} from "../../../public/assets/svgs/common";
import Image from "next/image";

type StickyHeaderPropsI = {
  userName?: string;
  userAvatar?: string;
  onSearch?: (query: string) => void;
  onNotificationClick?: () => void;
  onProfileClick?: () => void;
};

const StickyHeader: React.FC<StickyHeaderPropsI> = ({
  userName = "Vipin",
  userAvatar,
  onSearch,
  onNotificationClick,
  onProfileClick,
}) => {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    onSearch?.(value);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  return (
    <header className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm">
      <div className="px-6 py-1">
        <div className="flex items-center justify-between">
          {/* Welcome Message */}
          <div className="flex items-center">
            <h1 className="text-sm leading-6 font-medium text-[#0A0A0A]">
              Welcome {userName}
            </h1>
          </div>

          <div className="flex py-1 w-1/2 justify-end">
            <div className="flex-1 max-w-lg mx-8">
              <form onSubmit={handleSearchSubmit} className="relative">
                <div className="relative">
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    <SearchIconSimple className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search"
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="w-full pl-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  />
                </div>
              </form>
            </div>

            <div className="flex items-center gap-2">
              <button>
                <ChatIcon />
              </button>
              <button onClick={onNotificationClick}>
                <NotificationIcon />
              </button>

              <button onClick={onProfileClick}>
                {userAvatar ? (
                  <Image
                    src={userAvatar}
                    alt={userName}
                    className="h-8 w-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-8 w-8 rounded-full bg-gray-300" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default StickyHeader;
