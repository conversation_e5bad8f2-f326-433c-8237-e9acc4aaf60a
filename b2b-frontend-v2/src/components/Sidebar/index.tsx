"use client";

import { useLocale } from "next-intl";
import { getLangDir } from "rtl-detect";
import { cn } from "@/utils/class-merge";
import { SidebarProps } from "./types";
import { LanguageSupported } from "@/types/common/data";
import Logo from "../Logo";
import React, { useState } from "react";
import SidebarItem from "../SidebarItem";

const Sidebar: React.FC<SidebarProps> = ({
  items,
  hasLogo,
  title,
  className,
  level,
  collapsed = false,
  onToggleExpand,
  expandedItems,
  onToggleSidebar,
}) => {
  const locale = useLocale() as LanguageSupported;
  const direction = getLangDir(locale);
  const [internalExpandedItems, setInternalExpandedItems] = useState<
    Set<string>
  >(new Set());

  // Use external state if provided, otherwise use internal state
  const currentExpandedItems = expandedItems || internalExpandedItems;

  const handleToggleExpand = (itemHref: string) => {
    if (onToggleExpand) {
      onToggleExpand(itemHref);
    } else {
      // Use internal state management
      setInternalExpandedItems((prev) => {
        const newSet = new Set(prev);
        if (newSet.has(itemHref)) {
          newSet.delete(itemHref);
        } else {
          newSet.add(itemHref);
        }
        return newSet;
      });
    }
  };

  const regularItems = items.filter((item) => !item.isBottom);
  const bottomItems = items.filter((item) => item.isBottom);

  return (
    <aside
      className={cn(
        "flex flex-col h-screen z-[60] bg-white transition-all duration-300 ease-in-out",
        collapsed ? "w-16 p-3" : "w-64 pt-3 pb-3",
        !collapsed && (direction === "rtl" ? "pl-3" : "pr-3"),
        direction === "rtl"
          ? "border-l border-neutral-200"
          : "border-r border-neutral-200",
        className,
      )}
    >
      {hasLogo && (
        <div
          className={cn(
            "flex justify-center transition-all duration-300 ease-in-out",
            !collapsed && "pl-3",
          )}
        >
          <Logo
            compact={collapsed}
            imageSize={32}
            className="w-8 h-8 transition-all duration-300 ease-in-out"
          />
        </div>
      )}
      {!collapsed && title && (
        <span
          className={cn(
            "text-base font-medium text-neutral-950 transition-all duration-300 ease-in-out",
            direction === "rtl" ? "pr-3" : "pl-3",
          )}
        >
          {title}
        </span>
      )}
      {level === 1 && (
        <div className="mx-2 my-2 border-t border-neutral-200"></div>
      )}
      <nav className="space-y-3 mt-4 transition-all duration-300 ease-in-out">
        {regularItems.map((item, index) => (
          <SidebarItem
            key={index}
            {...item}
            level={level}
            direction={direction}
            locale={locale}
            collapsed={collapsed}
            onToggleExpand={handleToggleExpand}
            expandedItems={currentExpandedItems}
            onToggleSidebar={onToggleSidebar}
          />
        ))}
      </nav>
      <nav className="mt-auto space-y-3 transition-all duration-300 ease-in-out">
        {bottomItems.map((item, index) => (
          <SidebarItem
            key={index}
            {...item}
            level={level}
            direction={direction}
            locale={locale}
            collapsed={collapsed}
            onToggleExpand={handleToggleExpand}
            expandedItems={currentExpandedItems}
            onToggleSidebar={onToggleSidebar}
          />
        ))}
      </nav>
    </aside>
  );
};

export default Sidebar;
