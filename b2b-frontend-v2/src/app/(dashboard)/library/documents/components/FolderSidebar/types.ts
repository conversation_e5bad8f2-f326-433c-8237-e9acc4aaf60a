export type FileI = {
  id: string;
  name: string;
  type: "file";
  extension: string;
  size: number;
  lastModified: Date;
  path: string;
  parentId: string | null;
};

export type FolderI = {
  id: string;
  name: string;
  type: "folder";
  path: string;
  parentId: string | null;
  children: TreeNodeI[];
  isExpanded: boolean;
  count: number;
};

export type TreeNodeI = FileI | FolderI;

export type NavigationLinkI = {
  id: string;
  label: string;
  href: string;
  icon?: React.ReactNode;
};

export type DragItemI = {
  id: string;
  type: "file" | "folder";
  name: string;
  parentId: string | null;
};

export type DropTargetI = {
  id: string;
  type: "folder" | "root";
  accepts: ("file" | "folder")[];
};

export type CreateItemI = {
  type: "file" | "folder";
  name: string;
  parentId: string | null;
};

export type EditingStateI = {
  isEditing: boolean;
  itemId: string | null;
  itemType: "file" | "folder" | null;
  parentId: string | null;
};

export type LibraryTypeE = "public" | "private";

export type LibraryDataCacheI = {
  fileTree: TreeNodeI[];
  selectedItemId: string | null;
  expandedFolders: Set<string>;
};

export type SearchStateI = {
  isSearchMode: boolean;
  searchQuery: string;
};

export type FolderSidebarProps = {
  folders?: FolderI[];
  selectedFolder?: string | null;
  onFolderSelect?: (folderId: string | null) => void;

  fileTree?: TreeNodeI[];
  selectedItem?: string | null;
  onItemSelect?: (item: TreeNodeI | null) => void;
  onItemMove?: (
    itemId: string,
    newParentId: string | null,
    newIndex?: number,
    sourceLibraryType?: LibraryTypeE,
    targetLibraryType?: LibraryTypeE,
  ) => void;
  onItemCreate?: (item: CreateItemI) => void;
  onItemRename?: (itemId: string, newName: string) => void;
  onItemDelete?: (itemId: string) => void;

  enableDragDrop?: boolean;
  disableInternalDndContext?: boolean; // Disable internal DndContext when parent provides one
  enableInlineEditing?: boolean;
  maxDepth?: number;
  virtualScrolling?: boolean;

  onLibraryToggle?: (isExpanded: boolean) => void;
  onAddFile?: () => void;
  onAddFolder?: () => void;

  // New props for library type and split view
  libraryType?: LibraryTypeE;
  isSplitView?: boolean;
  onLibraryTypeChange?: (type: LibraryTypeE) => void;
  onSplitViewChange?: (isActive: boolean) => void; // Notify parent when split view is toggled
  allowDragFrom?: boolean; // For split view: allow dragging from this sidebar
  allowDropTo?: boolean; // For split view: allow dropping to this sidebar
};

export type TreeItemProps = {
  node: TreeNodeI;
  level: number;
  isSelected: boolean;
  isEditing: boolean;
  selectedItemId?: string | null; // ID of the selected item (for children to compute their state)
  editingItemId?: string | null; // ID of the item being edited (for children to compute their state)
  onSelect: (node: TreeNodeI) => void;
  onToggleExpand: (nodeId: string) => void;
  onStartEdit: (nodeId: string, type: "file" | "folder") => void;
  onFinishEdit: (nodeId: string, newName: string) => void;
  onCancelEdit: () => void;
  onDelete: (nodeId: string) => void;
  onCreateFile?: (parentId: string | null) => void;
  onCreateFolder?: (parentId: string | null) => void;
  enableDragDrop: boolean;
  maxDepth: number;
  allNodes?: TreeNodeI[];
  renderChildren?: boolean; // Controls whether TreeItem should render its own children
};

export type LibrarySectionProps = {
  isExpanded: boolean;
  onToggle: () => void;
  onAddFile: () => void;
  onAddFolder: () => void;
  addFileTooltip?: string;
  addFolderTooltip?: string;
  children: React.ReactNode;
};

export type FolderSidebarHookProps = {
  isSplitView: boolean;
  propLibraryType?: LibraryTypeE;
  fileTree: TreeNodeI[];
  onSplitViewChange?: (isActive: boolean) => void;
  selectedItem?: string | null;
  onLibraryTypeChange?: (type: LibraryTypeE) => void;
  folders: FolderI[];
  onLibraryToggle?: (isExpanded: boolean) => void;
  onItemSelect?: (item: TreeNodeI | null) => void;
  onFolderSelect?: (folderId: string | null) => void;
  onItemRename?: (itemId: string, newName: string) => void;
  onItemCreate?: (item: CreateItemI) => void;
  onItemDelete?: (itemId: string) => void;
  onAddFile?: () => void;
  onAddFolder?: () => void;
  onItemMove?: (
    itemId: string,
    newParentId: string | null,
    newIndex?: number,
    sourceLibraryType?: LibraryTypeE,
    targetLibraryType?: LibraryTypeE,
  ) => void;
};
