"use client";
import React, { useMemo } from "react";
import { DndContext, closestCenter } from "@dnd-kit/core";
import {
  SplitEditorIcon,
  SearchIcon,
  ChevronDownIcon,
} from "../../../../../../../public/assets/svgs/common";
import { FolderSidebarProps } from "./types";
import Progress from "../../../../../../components/Progress";
import TreeItem from "../TreeItem";
import { findNodeById } from "./utils";
import SidebarItem from "@/components/SidebarItem";
import LibrarySection from "../LibrarySection";
import DraggableTreeItem from "../DraggableTreeItem";
import ConfirmDialog from "../../../../../../components/ConfirmDialog";
import SplitViewSidebar from "../SplitViewSidebar";
import useFolderSidebar from "./useHook";
import { NAVIGATION_LINKS } from "./data";
import { useState, useRef, useEffect } from "react";

// Library Dropdown Component
const LibraryDropdown: React.FC<{
  currentLibraryType: string;
  onLibraryTypeToggle: () => void;
}> = ({ currentLibraryType, onLibraryTypeToggle }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleOutsideClick);
    return () => document.removeEventListener("mousedown", handleOutsideClick);
  }, []);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionSelect = () => {
    onLibraryTypeToggle();
    setIsOpen(false);
  };

  return (
    <div ref={dropdownRef} className="relative">
      <button
        onClick={handleToggle}
        className="flex items-center gap-1 text-sm font-semibold capitalize hover:bg-gray-100 px-2 py-1 rounded transition-colors"
      >
        {currentLibraryType} library
        <ChevronDownIcon />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]">
          <button
            onClick={handleOptionSelect}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg transition-colors"
          >
            {currentLibraryType === "public" ? "Private" : "Public"} library
          </button>
        </div>
      )}
    </div>
  );
};

const FolderSidebar: React.FC<FolderSidebarProps> = ({
  folders = [],
  onFolderSelect,
  fileTree = [],
  selectedItem,
  onItemSelect,
  onItemMove,
  onItemCreate,
  onItemRename,
  onItemDelete,
  enableDragDrop = true,
  disableInternalDndContext = false,
  maxDepth = 10,
  onLibraryToggle,
  onAddFile,
  onAddFolder,
  libraryType: propLibraryType,
  isSplitView = false,
  onLibraryTypeChange,
  onSplitViewChange,
}) => {
  const {
    handleSplitViewToggle,
    isSplitViewActive,
    sensors,
    handleLibraryTypeToggle,
    isLoadingLibrary,
    isLibraryExpanded,
    handleLibraryToggle,
    handleItemSelect,
    handleToggleExpand,
    editingState,
    handleStartEdit,
    handleCancelEdit,
    handleFinishEdit,
    handleDelete,
    handleCreateFile,
    handleCreateFolder,
    handleDrop,
    handleDragEnd,
    moveConfirmation,
    handleAddFile,
    handleAddFolder,
    internalFileTree,
    selectedItemId,
    currentLibraryType,
    confirmMoveFolder,
    cancelMoveFolder,
    resetSelection,
    searchState,
    handleSearchToggle,
    handleSearchQueryChange,
  } = useFolderSidebar({
    isSplitView,
    propLibraryType,
    fileTree,
    onSplitViewChange,
    selectedItem,
    onLibraryTypeChange,
    folders,
    onLibraryToggle,
    onItemSelect,
    onFolderSelect,
    onItemRename,
    onAddFile,
    onAddFolder,
    onItemMove,
    onItemCreate,
    onItemDelete,
  });

  const renderedTree = useMemo(() => {
    if (internalFileTree.length === 0) {
      return (
        <div className="text-center py-8 text-neutral-500">
          <p className="text-sm">No files or folders</p>
          <p className="text-xs mt-1">Click the + buttons to add content</p>
        </div>
      );
    }

    return internalFileTree.map((node) => {
      if (enableDragDrop) {
        return (
          <DraggableTreeItem
            key={node.id}
            node={node}
            level={0}
            isSelected={selectedItemId === node.id}
            isEditing={
              editingState.isEditing && editingState.itemId === node.id
            }
            selectedItemId={selectedItemId}
            editingItemId={editingState.itemId}
            onSelect={handleItemSelect}
            onToggleExpand={handleToggleExpand}
            onStartEdit={handleStartEdit}
            onFinishEdit={handleFinishEdit}
            onCancelEdit={handleCancelEdit}
            onDelete={handleDelete}
            onCreateFile={handleCreateFile}
            onCreateFolder={handleCreateFolder}
            onDrop={handleDrop}
            enableDragDrop={enableDragDrop}
            maxDepth={maxDepth}
            allNodes={internalFileTree}
          />
        );
      } else {
        return (
          <TreeItem
            key={node.id}
            node={node}
            level={0}
            isSelected={selectedItemId === node.id}
            isEditing={
              editingState.isEditing && editingState.itemId === node.id
            }
            selectedItemId={selectedItemId}
            editingItemId={editingState.itemId}
            onSelect={handleItemSelect}
            onToggleExpand={handleToggleExpand}
            onStartEdit={handleStartEdit}
            onFinishEdit={handleFinishEdit}
            onCancelEdit={handleCancelEdit}
            onDelete={handleDelete}
            onCreateFile={handleCreateFile}
            onCreateFolder={handleCreateFolder}
            enableDragDrop={enableDragDrop}
            maxDepth={maxDepth}
            allNodes={internalFileTree}
          />
        );
      }
    });
  }, [
    internalFileTree,
    selectedItemId,
    editingState,
    enableDragDrop,
    maxDepth,
    handleItemSelect,
    handleToggleExpand,
    handleStartEdit,
    handleFinishEdit,
    handleCancelEdit,
    handleDelete,
    handleCreateFile,
    handleCreateFolder,
    handleDrop,
  ]);

  // If split view is active and this is not already in split view mode, render SplitViewSidebar
  if (isSplitViewActive && !isSplitView) {
    return (
      <SplitViewSidebar
        onClose={handleSplitViewToggle}
        onItemMove={(itemId, newParentId, sourceLibrary, targetLibrary) => {
          onItemMove?.(
            itemId,
            newParentId,
            undefined,
            sourceLibrary,
            targetLibrary,
          );
        }}
        onItemCreate={(item) => {
          onItemCreate?.(item);
        }}
        onItemRename={(itemId, newName) => {
          onItemRename?.(itemId, newName);
        }}
        onItemDelete={(itemId) => {
          onItemDelete?.(itemId);
        }}
      />
    );
  }

  return (
    <div className="h-full min-h-screen bg-white border-r border-neutral-200 p-4 flex flex-col">
      <div className="flex-shrink-0 flex items-center justify-between gap-2 mb-4">
        {!isSplitView && (
          <LibraryDropdown
            currentLibraryType={currentLibraryType}
            onLibraryTypeToggle={handleLibraryTypeToggle}
          />
        )}
        {isSplitView && (
          <h3 className="text-sm font-semibold capitalize">
            {currentLibraryType} library
          </h3>
        )}

        {searchState.isSearchMode ? (
          <input
            type="text"
            placeholder="Search files and folders..."
            value={searchState.searchQuery}
            onChange={(e) => handleSearchQueryChange(e.target.value)}
            className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
            autoFocus
          />
        ) : (
          <Progress progress={80} />
        )}

        <div className="flex items-center gap-1">
          <SearchIcon
            height={14}
            width={14}
            isActive={searchState.isSearchMode}
            onClick={handleSearchToggle}
          />
          {!isSplitView && (
            <SplitEditorIcon
              height={14}
              width={14}
              isActive={isSplitViewActive}
              onClick={handleSplitViewToggle}
            />
          )}
        </div>
      </div>

      <div className="flex-shrink-0 space-y-1 mb-1">
        {NAVIGATION_LINKS.map((link) => (
          <SidebarItem
            isCustomSideItem
            key={link.id}
            direction="ltr"
            href={link.href}
            label={link.label}
            locale="en"
            level={0}
          />
        ))}
      </div>

      <div className="flex-1 min-h-0 flex flex-col">
        <LibrarySection
          isExpanded={isLibraryExpanded}
          onToggle={handleLibraryToggle}
          onAddFile={handleAddFile}
          onAddFolder={handleAddFolder}
          addFileTooltip={
            selectedItemId &&
            findNodeById(internalFileTree, selectedItemId)?.type === "folder"
              ? "Create a new file in the selected folder"
              : "Create a new file in the root directory"
          }
          addFolderTooltip={
            selectedItemId &&
            findNodeById(internalFileTree, selectedItemId)?.type === "folder"
              ? "Create a new folder in the selected folder"
              : "Create a new folder in the root directory"
          }
        >
          <div
            className="flex-1 overflow-y-auto overflow-x-auto h-full relative"
            onClick={resetSelection}
          >
            {isLoadingLibrary ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              </div>
            ) : enableDragDrop && !disableInternalDndContext ? (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
                onDragStart={() => {}}
              >
                {renderedTree}
              </DndContext>
            ) : (
              renderedTree
            )}
          </div>
        </LibrarySection>
      </div>

      <ConfirmDialog
        isOpen={moveConfirmation.isOpen}
        title="Move Folder"
        message={`Are you sure you want to move "${moveConfirmation.draggedName}" into "${moveConfirmation.targetName}"?`}
        confirmText="Move"
        cancelText="Cancel"
        variant="warning"
        onConfirm={confirmMoveFolder}
        onCancel={cancelMoveFolder}
      />
    </div>
  );
};

export default FolderSidebar;
