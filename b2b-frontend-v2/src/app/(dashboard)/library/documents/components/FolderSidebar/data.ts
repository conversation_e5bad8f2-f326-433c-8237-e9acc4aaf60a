import { NavigationLinkI, TreeNodeI } from "./types";

export const sampleFileTree: TreeNodeI[] = [
  {
    id: "folder-1",
    name: "Documents",
    type: "folder",
    path: "/Documents",
    parentId: null,
    isExpanded: true,
    count: 3,
    children: [
      {
        id: "file-1",
        name: "Project Plan.docx",
        type: "file",
        extension: "docx",
        size: 2048000,
        lastModified: new Date("2024-01-15"),
        path: "/Documents/Project Plan.docx",
        parentId: "folder-1",
      },
      {
        id: "file-2",
        name: "Budget.xlsx",
        type: "file",
        extension: "xlsx",
        size: 1024000,
        lastModified: new Date("2024-01-10"),
        path: "/Documents/Budget.xlsx",
        parentId: "folder-1",
      },
      {
        id: "folder-2",
        name: "Reports",
        type: "folder",
        path: "/Documents/Reports",
        parentId: "folder-1",
        isExpanded: false,
        count: 2,
        children: [
          {
            id: "file-3",
            name: "Q1 Report.pdf",
            type: "file",
            extension: "pdf",
            size: 3072000,
            lastModified: new Date("2024-01-20"),
            path: "/Documents/Reports/Q1 Report.pdf",
            parentId: "folder-2",
          },
          {
            id: "file-4",
            name: "Analysis.pdf",
            type: "file",
            extension: "pdf",
            size: 1536000,
            lastModified: new Date("2024-01-18"),
            path: "/Documents/Reports/Analysis.pdf",
            parentId: "folder-2",
          },
        ],
      },
    ],
  },
  {
    id: "folder-3",
    name: "Images",
    type: "folder",
    path: "/Images",
    parentId: null,
    isExpanded: false,
    count: 2,
    children: [
      {
        id: "file-5",
        name: "logo.png",
        type: "file",
        extension: "png",
        size: 512000,
        lastModified: new Date("2024-01-05"),
        path: "/Images/logo.png",
        parentId: "folder-3",
      },
      {
        id: "file-6",
        name: "banner.jpg",
        type: "file",
        extension: "jpg",
        size: 1024000,
        lastModified: new Date("2024-01-08"),
        path: "/Images/banner.jpg",
        parentId: "folder-3",
      },
    ],
  },
  {
    id: "file-7",
    name: "README.txt",
    type: "file",
    extension: "txt",
    size: 4096,
    lastModified: new Date("2024-01-01"),
    path: "/README.txt",
    parentId: null,
  },
];

export const createLargeFileTree = (
  depth: number = 3,
  itemsPerLevel: number = 10,
): TreeNodeI[] => {
  const createNode = (
    id: string,
    name: string,
    level: number,
    parentId: string | null,
  ): TreeNodeI => {
    const isFolder = level < depth && Math.random() > 0.3;

    if (isFolder) {
      const children: TreeNodeI[] = [];
      for (let i = 0; i < itemsPerLevel; i++) {
        children.push(
          createNode(
            `${id}-${i}`,
            `${level < depth - 1 ? "Folder" : "File"} ${i + 1}`,
            level + 1,
            id,
          ),
        );
      }

      return {
        id,
        name,
        type: "folder",
        path: `/${name}`,
        parentId,
        isExpanded: false,
        count: children.length,
        children,
      };
    } else {
      const extensions = ["txt", "pdf", "docx", "xlsx", "png", "jpg"];
      const extension =
        extensions[Math.floor(Math.random() * extensions.length)];

      return {
        id,
        name: `${name}.${extension}`,
        type: "file",
        extension,
        size: Math.floor(Math.random() * 5000000) + 1000,
        lastModified: new Date(
          Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000,
        ),
        path: `/${name}.${extension}`,
        parentId,
      };
    }
  };

  const tree: TreeNodeI[] = [];
  for (let i = 0; i < itemsPerLevel; i++) {
    tree.push(createNode(`root-${i}`, `Item ${i + 1}`, 0, null));
  }

  return tree;
};

// Sample data for public library
export const samplePublicLibraryTree: TreeNodeI[] = [
  {
    id: "public-folder-1",
    name: "Company Policies",
    type: "folder",
    path: "/Company Policies",
    parentId: null,
    isExpanded: true,
    count: 3,
    children: [
      {
        id: "public-file-1",
        name: "Code of Conduct.pdf",
        type: "file",
        extension: "pdf",
        size: 1024000,
        lastModified: new Date("2024-01-15"),
        path: "/Company Policies/Code of Conduct.pdf",
        parentId: "public-folder-1",
      },
      {
        id: "public-file-2",
        name: "Safety Guidelines.docx",
        type: "file",
        extension: "docx",
        size: 2048000,
        lastModified: new Date("2024-01-10"),
        path: "/Company Policies/Safety Guidelines.docx",
        parentId: "public-folder-1",
      },
      {
        id: "public-file-3",
        name: "Employee Handbook.pdf",
        type: "file",
        extension: "pdf",
        size: 3072000,
        lastModified: new Date("2024-01-20"),
        path: "/Company Policies/Employee Handbook.pdf",
        parentId: "public-folder-1",
      },
    ],
  },
  {
    id: "public-folder-2",
    name: "Training Materials",
    type: "folder",
    path: "/Training Materials",
    parentId: null,
    isExpanded: false,
    count: 2,
    children: [
      {
        id: "public-file-4",
        name: "Onboarding Guide.pdf",
        type: "file",
        extension: "pdf",
        size: 1536000,
        lastModified: new Date("2024-01-18"),
        path: "/Training Materials/Onboarding Guide.pdf",
        parentId: "public-folder-2",
      },
      {
        id: "public-file-5",
        name: "Software Training.pptx",
        type: "file",
        extension: "pptx",
        size: 4096000,
        lastModified: new Date("2024-01-12"),
        path: "/Training Materials/Software Training.pptx",
        parentId: "public-folder-2",
      },
    ],
  },
];

// Sample data for private library
export const samplePrivateLibraryTree: TreeNodeI[] = [
  {
    id: "private-folder-1",
    name: "Personal Documents",
    type: "folder",
    path: "/Personal Documents",
    parentId: null,
    isExpanded: true,
    count: 2,
    children: [
      {
        id: "private-file-1",
        name: "Resume.pdf",
        type: "file",
        extension: "pdf",
        size: 512000,
        lastModified: new Date("2024-02-01"),
        path: "/Personal Documents/Resume.pdf",
        parentId: "private-folder-1",
      },
      {
        id: "private-file-2",
        name: "Cover Letter.docx",
        type: "file",
        extension: "docx",
        size: 256000,
        lastModified: new Date("2024-02-05"),
        path: "/Personal Documents/Cover Letter.docx",
        parentId: "private-folder-1",
      },
    ],
  },
  {
    id: "private-folder-2",
    name: "Project Drafts",
    type: "folder",
    path: "/Project Drafts",
    parentId: null,
    isExpanded: false,
    count: 3,
    children: [
      {
        id: "private-file-3",
        name: "Proposal Draft.docx",
        type: "file",
        extension: "docx",
        size: 1024000,
        lastModified: new Date("2024-02-10"),
        path: "/Project Drafts/Proposal Draft.docx",
        parentId: "private-folder-2",
      },
      {
        id: "private-file-4",
        name: "Budget Estimate.xlsx",
        type: "file",
        extension: "xlsx",
        size: 768000,
        lastModified: new Date("2024-02-08"),
        path: "/Project Drafts/Budget Estimate.xlsx",
        parentId: "private-folder-2",
      },
      {
        id: "private-file-5",
        name: "Timeline.pdf",
        type: "file",
        extension: "pdf",
        size: 384000,
        lastModified: new Date("2024-02-12"),
        path: "/Project Drafts/Timeline.pdf",
        parentId: "private-folder-2",
      },
    ],
  },
  {
    id: "private-folder-3",
    name: "Notes",
    type: "folder",
    path: "/Notes",
    parentId: null,
    isExpanded: false,
    count: 1,
    children: [
      {
        id: "private-file-6",
        name: "Meeting Notes.txt",
        type: "file",
        extension: "txt",
        size: 8192,
        lastModified: new Date("2024-02-15"),
        path: "/Notes/Meeting Notes.txt",
        parentId: "private-folder-3",
      },
    ],
  },
];

export const NAVIGATION_LINKS: NavigationLinkI[] = [
  {
    id: "overview",
    label: "globalSidebar.navigation.library.overview",
    href: "/library/overview",
  },
  {
    id: "drafts",
    label: "globalSidebar.navigation.library.my_drafts",
    href: "/library/my-drafts",
  },
  {
    id: "approvals",
    label: "globalSidebar.navigation.library.approvals",
    href: "/library/approvals",
  },
];
