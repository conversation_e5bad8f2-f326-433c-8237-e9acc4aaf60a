import { useCallback, useEffect, useRef, useState } from "react";
import {
  EditingStateI,
  FolderI,
  FolderSidebarHookProps,
  LibraryDataCacheI,
  LibraryTypeE,
  SearchStateI,
  TreeNodeI,
} from "./types";
import { samplePrivateLibraryTree, samplePublicLibraryTree } from "./data";
import {
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  addNodeToTree,
  createNewFile,
  createNewFolder,
  findNodeById,
  getSiblingNodes,
  moveNodeInTree,
  removeNodeFromTree,
  updateNodeInTree,
  validateFileName,
  validateFolderName,
} from "./utils";

const useFolderSidebar = (props: FolderSidebarHookProps) => {
  const {
    fileTree,
    isSplitView,
    propLibraryType,
    onSplitViewChange,
    selectedItem,
    onLibraryTypeChange,
    folders,
    onLibraryToggle,
    onItemSelect,
    onFolderSelect,
    onItemRename,
    onItemCreate,
    onItemDelete,
    onAddFile,
    onAddFolder,
    onItemMove,
  } = props;

  const libraryDataCache = useRef<Record<LibraryTypeE, LibraryDataCacheI>>({
    public: {
      fileTree: samplePublicLibraryTree,
      selectedItemId: null,
      expandedFolders: new Set<string>(),
    },
    private: {
      fileTree: samplePrivateLibraryTree,
      selectedItemId: null,
      expandedFolders: new Set<string>(),
    },
  });

  const [isSplitViewActive, setIsSplitViewActive] = useState(false);
  const [isLoadingLibrary, setIsLoadingLibrary] = useState(false);

  const [currentLibraryType, setCurrentLibraryType] = useState<LibraryTypeE>(
    propLibraryType || "public",
  );
  const [selectedItemId, setSelectedItemId] = useState<string | null>(
    selectedItem || null,
  );

  const [internalFileTree, setInternalFileTree] = useState<TreeNodeI[]>(
    isSplitView && propLibraryType
      ? libraryDataCache.current[propLibraryType].fileTree
      : fileTree.length > 0
        ? fileTree
        : libraryDataCache.current[currentLibraryType].fileTree,
  );

  const [editingState, setEditingState] = useState<EditingStateI>({
    isEditing: false,
    itemId: null,
    itemType: null,
    parentId: null,
  });

  const [moveConfirmation, setMoveConfirmation] = useState<{
    isOpen: boolean;
    draggedId: string;
    targetId: string;
    draggedName: string;
    targetName: string;
  }>({
    isOpen: false,
    draggedId: "",
    targetId: "",
    draggedName: "",
    targetName: "",
  });

  const [searchState, setSearchState] = useState<SearchStateI>({
    isSearchMode: false,
    searchQuery: "",
  });

  useEffect(() => {
    if (isSplitView && fileTree && fileTree.length > 0) {
      setInternalFileTree(fileTree);
    }
  }, [fileTree, isSplitView, propLibraryType]);

  const hasContent = internalFileTree.length > 0 || folders.length > 0;
  const isOnDocumentsPage =
    typeof window !== "undefined" &&
    window.location.pathname.includes("/library/documents");

  const [isLibraryExpanded, setIsLibraryExpanded] = useState(
    hasContent || isOnDocumentsPage ? true : false,
  );

  const saveCurrentLibraryToCache = useCallback(() => {
    const expandedFolders = new Set<string>();
    const collectExpandedFolders = (nodes: TreeNodeI[]) => {
      nodes.forEach((node) => {
        if (node.type === "folder" && node.isExpanded) {
          expandedFolders.add(node.id);
          if (node.children) {
            collectExpandedFolders(node.children);
          }
        }
      });
    };
    collectExpandedFolders(internalFileTree);

    libraryDataCache.current[currentLibraryType] = {
      fileTree: internalFileTree,
      selectedItemId,
      expandedFolders,
    };
  }, [internalFileTree, selectedItemId, currentLibraryType]);

  const handleSplitViewToggle = () => {
    if (!isSplitView) {
      saveCurrentLibraryToCache();
    }
    const newSplitViewState = !isSplitViewActive;
    setIsSplitViewActive(newSplitViewState);

    // Notify parent component about split view state change
    onSplitViewChange?.(newSplitViewState);
  };

  const handleLibraryTypeToggle = useCallback(() => {
    if (isSplitView) return;
    saveCurrentLibraryToCache();

    const newLibraryType: LibraryTypeE =
      currentLibraryType === "public" ? "private" : "public";

    setIsLoadingLibrary(true);

    setTimeout(() => {
      const cachedData = libraryDataCache.current[newLibraryType];
      setInternalFileTree(cachedData.fileTree);
      setSelectedItemId(cachedData.selectedItemId);
      setCurrentLibraryType(newLibraryType);
      setIsLoadingLibrary(false);
      onLibraryTypeChange?.(newLibraryType);
    }, 300);
  }, [currentLibraryType, isSplitView]);

  const handleLibraryToggle = useCallback(() => {
    const newExpanded = !isLibraryExpanded;
    setIsLibraryExpanded(newExpanded);
    onLibraryToggle?.(newExpanded);
  }, [isLibraryExpanded]);

  const handleItemSelect = useCallback(
    (item: TreeNodeI | null) => {
      const itemId = item?.id || null;
      setSelectedItemId(itemId);
      onItemSelect?.(item);

      if (item && item.type === "folder") {
        onFolderSelect?.(item.name);

        if (!item.isExpanded) {
          setInternalFileTree((prevTree) =>
            updateNodeInTree(prevTree, item.id, {
              isExpanded: true,
            }),
          );
        }
      }
    },
    [onItemSelect, onFolderSelect],
  );

  const handleToggleExpand = useCallback((nodeId: string) => {
    setInternalFileTree((prevTree) => {
      const node = findNodeById(prevTree, nodeId);
      if (node && node.type === "folder") {
        return updateNodeInTree(prevTree, nodeId, {
          isExpanded: !node.isExpanded,
        });
      }
      return prevTree;
    });
  }, []);

  const handleStartEdit = useCallback(
    (itemId: string, itemType: "file" | "folder") => {
      setEditingState({
        isEditing: true,
        itemId,
        itemType,
        parentId: findNodeById(internalFileTree, itemId)?.parentId || null,
      });
    },
    [internalFileTree],
  );

  const handleFinishEdit = useCallback(
    (itemId: string, newName: string) => {
      if (!newName.trim()) {
        setEditingState({
          isEditing: false,
          itemId: null,
          itemType: null,
          parentId: null,
        });
        return;
      }

      const node = findNodeById(internalFileTree, itemId);
      if (!node) return;

      const siblingNodes = getSiblingNodes(internalFileTree, itemId);

      const validation =
        node.type === "folder"
          ? validateFolderName(newName, siblingNodes, itemId)
          : validateFileName(newName, siblingNodes, itemId);

      if (!validation.isValid) {
        alert(validation.error);
        return;
      }

      const updatedTree = updateNodeInTree(internalFileTree, itemId, {
        name: newName,
      });
      setInternalFileTree(updatedTree);
      onItemRename?.(itemId, newName);

      setEditingState({
        isEditing: false,
        itemId: null,
        itemType: null,
        parentId: null,
      });
    },
    [internalFileTree],
  );

  const handleCancelEdit = useCallback(() => {
    setEditingState({
      isEditing: false,
      itemId: null,
      itemType: null,
      parentId: null,
    });
  }, []);

  const handleDelete = useCallback(
    (itemId: string) => {
      const updatedTree = removeNodeFromTree(internalFileTree, itemId);
      setInternalFileTree(updatedTree);
      onItemDelete?.(itemId);

      if (selectedItemId === itemId) {
        setSelectedItemId(null);
        onItemSelect?.(null);
      }
    },
    [internalFileTree, selectedItemId],
  );

  const handleAddFile = useCallback(() => {
    let targetParentId: string | null = null;
    if (selectedItemId) {
      const selectedNode = findNodeById(internalFileTree, selectedItemId);
      if (selectedNode?.type === "folder") {
        targetParentId = selectedItemId;
      }
    }

    const siblingNodes = targetParentId
      ? findNodeById(internalFileTree, targetParentId)?.type === "folder"
        ? (findNodeById(internalFileTree, targetParentId) as FolderI)
            ?.children || []
        : internalFileTree.filter((n) => n.parentId === null)
      : internalFileTree.filter((n) => n.parentId === null);

    const newFile = createNewFile("New File.txt", targetParentId, siblingNodes);
    const updatedTree = addNodeToTree(
      internalFileTree,
      newFile,
      targetParentId,
    );
    setInternalFileTree(updatedTree);
    onItemCreate?.({
      type: "file",
      name: newFile.name,
      parentId: targetParentId,
    });

    setTimeout(() => {
      handleStartEdit(newFile.id, "file");
    }, 0);

    onAddFile?.();
  }, [internalFileTree, selectedItemId]);

  const handleAddFolder = useCallback(() => {
    let targetParentId: string | null = null;
    if (selectedItemId) {
      const selectedNode = findNodeById(internalFileTree, selectedItemId);
      if (selectedNode?.type === "folder") {
        targetParentId = selectedItemId;
      }
    }

    const siblingNodes = targetParentId
      ? findNodeById(internalFileTree, targetParentId)?.type === "folder"
        ? (findNodeById(internalFileTree, targetParentId) as FolderI)
            ?.children || []
        : internalFileTree.filter((n) => n.parentId === null)
      : internalFileTree.filter((n) => n.parentId === null);

    const newFolder = createNewFolder(
      "New Folder",
      targetParentId,
      siblingNodes,
    );
    const updatedTree = addNodeToTree(
      internalFileTree,
      newFolder,
      targetParentId,
    );
    setInternalFileTree(updatedTree);
    onItemCreate?.({
      type: "folder",
      name: newFolder.name,
      parentId: targetParentId,
    });

    setTimeout(() => {
      handleStartEdit(newFolder.id, "folder");
    }, 0);

    onAddFolder?.();
  }, [internalFileTree, selectedItemId]);

  const handleCreateFile = useCallback(
    (parentId: string | null) => {
      const siblingNodes = parentId
        ? findNodeById(internalFileTree, parentId)?.type === "folder"
          ? (findNodeById(internalFileTree, parentId) as FolderI)?.children ||
            []
          : []
        : internalFileTree.filter((node) => node.parentId === null);

      const newFile = createNewFile("New File.txt", parentId, siblingNodes);
      const updatedTree = addNodeToTree(internalFileTree, newFile, parentId);
      setInternalFileTree(updatedTree);
      onItemCreate?.({ type: "file", name: newFile.name, parentId });

      if (parentId) {
        const parentNode = findNodeById(internalFileTree, parentId);
        if (
          parentNode &&
          parentNode.type === "folder" &&
          !parentNode.isExpanded
        ) {
          const expandedTree = updateNodeInTree(updatedTree, parentId, {
            isExpanded: true,
          });
          setInternalFileTree(expandedTree);
        }
      }

      setTimeout(() => {
        handleStartEdit(newFile.id, "file");
      }, 0);
    },
    [internalFileTree],
  );

  const handleCreateFolder = useCallback(
    (parentId: string | null) => {
      const siblingNodes = parentId
        ? findNodeById(internalFileTree, parentId)?.type === "folder"
          ? (findNodeById(internalFileTree, parentId) as FolderI)?.children ||
            []
          : []
        : internalFileTree.filter((node) => node.parentId === null);

      const newFolder = createNewFolder("New Folder", parentId, siblingNodes);
      const updatedTree = addNodeToTree(internalFileTree, newFolder, parentId);
      setInternalFileTree(updatedTree);
      onItemCreate?.({ type: "folder", name: newFolder.name, parentId });

      if (parentId) {
        const parentNode = findNodeById(internalFileTree, parentId);
        if (
          parentNode &&
          parentNode.type === "folder" &&
          !parentNode.isExpanded
        ) {
          const expandedTree = updateNodeInTree(updatedTree, parentId, {
            isExpanded: true,
          });
          setInternalFileTree(expandedTree);
        }
      }

      setTimeout(() => {
        handleStartEdit(newFolder.id, "folder");
      }, 0);
    },
    [internalFileTree],
  );

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over || active.id === over.id) return;

      const draggedId = active.id as string;
      const targetId = over.id as string;

      const actualTargetId = targetId.startsWith("drop-")
        ? targetId.slice(5)
        : targetId;

      const draggedNode = findNodeById(internalFileTree, draggedId);
      const targetNode = findNodeById(internalFileTree, actualTargetId);

      if (!draggedNode || !targetNode) return;

      if (targetNode.type === "file") {
        return;
      }

      if (draggedNode.type === "folder") {
        setMoveConfirmation({
          isOpen: true,
          draggedId,
          targetId: actualTargetId,
          draggedName: draggedNode.name,
          targetName: targetNode.name,
        });
      } else {
        const updatedTree = moveNodeInTree(
          internalFileTree,
          draggedId,
          actualTargetId,
        );
        setInternalFileTree(updatedTree);
        onItemMove?.(draggedId, actualTargetId);
      }
    },
    [internalFileTree],
  );

  const handleDrop = useCallback(
    (
      draggedId: string,
      targetId: string,
      // position: "before" | "after" | "inside",
    ) => {
      const updatedTree = moveNodeInTree(internalFileTree, draggedId, targetId);
      setInternalFileTree(updatedTree);
      onItemMove?.(draggedId, targetId);
    },
    [internalFileTree],
  );

  const confirmMoveFolder = () => {
    const updatedTree = moveNodeInTree(
      internalFileTree,
      moveConfirmation.draggedId,
      moveConfirmation.targetId,
    );
    setInternalFileTree(updatedTree);
    onItemMove?.(moveConfirmation.draggedId, moveConfirmation.targetId);
    setMoveConfirmation({
      isOpen: false,
      draggedId: "",
      targetId: "",
      draggedName: "",
      targetName: "",
    });
  };

  const cancelMoveFolder = () =>
    setMoveConfirmation({
      isOpen: false,
      draggedId: "",
      targetId: "",
      draggedName: "",
      targetName: "",
    });

  const resetSelection = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      setSelectedItemId(null);
      onItemSelect?.(null);
    }
  };

  const handleSearchToggle = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      isSearchMode: !prev.isSearchMode,
      searchQuery: prev.isSearchMode ? "" : prev.searchQuery,
    }));
  }, []);

  const handleSearchQueryChange = useCallback((query: string) => {
    setSearchState(prev => ({
      ...prev,
      searchQuery: query,
    }));
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
  );

  return {
    saveCurrentLibraryToCache,
    handleSplitViewToggle,
    isSplitViewActive,
    sensors,
    handleLibraryTypeToggle,
    isLoadingLibrary,
    isLibraryExpanded,
    handleLibraryToggle,
    handleItemSelect,
    handleToggleExpand,
    editingState,
    handleStartEdit,
    handleFinishEdit,
    handleCancelEdit,
    handleDelete,
    handleCreateFile,
    handleCreateFolder,
    handleDrop,
    handleDragEnd,
    moveConfirmation,
    handleAddFile,
    handleAddFolder,
    internalFileTree,
    selectedItemId,
    currentLibraryType,
    confirmMoveFolder,
    cancelMoveFolder,
    resetSelection,
    searchState,
    handleSearchToggle,
    handleSearchQueryChange,
  };
};

export default useFolderSidebar;
