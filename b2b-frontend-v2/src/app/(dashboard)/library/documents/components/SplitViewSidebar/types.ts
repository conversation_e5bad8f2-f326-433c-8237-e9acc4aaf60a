import { LibraryTypeE } from "../FolderSidebar/types";

export type SplitViewSidebarPropsI = {
  onClose: () => void;
  onItemMove?: (
    itemId: string,
    newParentId: string | null,
    sourceLibraryType: LibraryTypeE,
    targetLibraryType: LibraryTypeE,
  ) => void;
  onItemCreate?: (
    item: { type: "file" | "folder"; name: string; parentId: string | null },
    libraryType: LibraryTypeE,
  ) => void;
  onItemRename?: (
    itemId: string,
    newName: string,
    libraryType: LibraryTypeE,
  ) => void;
  onItemDelete?: (itemId: string, libraryType: LibraryTypeE) => void;
};
