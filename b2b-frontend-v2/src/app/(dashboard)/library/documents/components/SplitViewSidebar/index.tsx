"use client";

import React, { useState, useCallback } from "react";
import {
  Dnd<PERSON>ontext,
  DragEndEvent,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
  useDroppable,
} from "@dnd-kit/core";
import FolderSidebar from "../FolderSidebar";
import { TreeNodeI, LibraryTypeE } from "../FolderSidebar/types";
import {
  samplePublicLibraryTree,
  samplePrivateLibraryTree,
} from "../FolderSidebar/data";
import {
  findNodeById,
  removeNodeFromTree,
  addNodeToTree,
  moveNodeInTree,
} from "../FolderSidebar/utils";
import ConfirmDialog from "../../../../../../components/ConfirmDialog";
import { SplitViewSidebarPropsI } from "./types";

const SplitViewSidebar: React.FC<SplitViewSidebarPropsI> = ({
  onClose,
  onItemMove,
  onItemCreate,
  onItemRename,
  onItemDelete,
}) => {
  const [publicFileTree, setPublicFileTree] = useState<TreeNodeI[]>(
    samplePublicLibraryTree,
  );
  const [privateFileTree, setPrivateFileTree] = useState<TreeNodeI[]>(
    samplePrivateLibraryTree,
  );
  const [publicSelectedItem, setPublicSelectedItem] = useState<string | null>(
    null,
  );
  const [privateSelectedItem, setPrivateSelectedItem] = useState<string | null>(
    null,
  );
  const [activeId, setActiveId] = useState<string | null>(null);
  const [dragSource, setDragSource] = useState<LibraryTypeE | null>(null);
  const [invalidDropDialog, setInvalidDropDialog] = useState<{
    isOpen: boolean;
    message: string;
  }>({
    isOpen: false,
    message: "",
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3, // Reduced distance for easier drag activation
      },
    }),
  );

  const handleDragStart = useCallback(
    (event: { active: { id: string } }) => {
      console.log("=== DRAG START DEBUG ===");
      console.log("Drag started:", event.active.id);
      setActiveId(event.active.id);

      // Debug: Log tree sizes and sample IDs
      console.log("Tree debugging:");
      console.log("- publicFileTree length:", publicFileTree.length);
      console.log("- privateFileTree length:", privateFileTree.length);
      console.log("- First public node ID:", publicFileTree[0]?.id);
      console.log("- First private node ID:", privateFileTree[0]?.id);

      // Test findNodeById function with known IDs
      const testPublicFind = findNodeById(publicFileTree, "public-file-1");
      const testPrivateFind = findNodeById(privateFileTree, "private-file-6");
      console.log("Function test results:");
      console.log(
        "- findNodeById(publicFileTree, 'public-file-1'):",
        !!testPublicFind,
        testPublicFind?.name,
      );
      console.log(
        "- findNodeById(privateFileTree, 'private-file-6'):",
        !!testPrivateFind,
        testPrivateFind?.name,
      );

      // Determine which library the drag started from
      const isFromPublic = findNodeById(publicFileTree, event.active.id);
      const isFromPrivate = findNodeById(privateFileTree, event.active.id);

      console.log("Source detection results:");
      console.log("- isFromPublic:", isFromPublic);
      console.log(
        "- isFromPublic node:",
        isFromPublic
          ? {
              id: isFromPublic.id,
              name: isFromPublic.name,
              type: isFromPublic.type,
            }
          : null,
      );
      console.log("- isFromPrivate:", isFromPrivate);
      console.log(
        "- isFromPrivate node:",
        isFromPrivate
          ? {
              id: isFromPrivate.id,
              name: isFromPrivate.name,
              type: isFromPrivate.type,
            }
          : null,
      );

      if (isFromPublic) {
        console.log("Dragging from public library");
        setDragSource("public");
      } else if (isFromPrivate) {
        console.log("Dragging from private library");
        setDragSource("private");
      } else {
        console.log("ERROR: Node not found in either library!");
        setDragSource(null);
      }
      console.log("=== DRAG START COMPLETE ===");
    },
    [publicFileTree, privateFileTree],
  );

  // Function to perform the actual tree manipulation
  const performMove = useCallback(
    (
      itemId: string,
      newParentId: string | null,
      sourceLibrary: LibraryTypeE,
      targetLibrary: LibraryTypeE,
    ) => {
      console.log("=== PERFORM MOVE DEBUG ===");
      console.log(
        `Performing move: ${itemId} from ${sourceLibrary} to ${targetLibrary}, parent: ${newParentId}`,
      );
      console.log("Current trees before move:");
      console.log("- publicFileTree length:", publicFileTree.length);
      console.log("- privateFileTree length:", privateFileTree.length);

      if (sourceLibrary === targetLibrary) {
        console.log("Same library move detected");
        // Same library move - use moveNodeInTree
        if (sourceLibrary === "public") {
          console.log("Moving within public library");
          const updatedTree = moveNodeInTree(
            publicFileTree,
            itemId,
            newParentId,
          );
          console.log("Updated public tree length:", updatedTree.length);
          console.log("Tree changed:", updatedTree !== publicFileTree);
          console.log("About to call setPublicFileTree...");
          setPublicFileTree(updatedTree);
          console.log("setPublicFileTree called successfully");
        } else {
          console.log("Moving within private library");
          const updatedTree = moveNodeInTree(
            privateFileTree,
            itemId,
            newParentId,
          );
          console.log("Updated private tree length:", updatedTree.length);
          console.log("Tree changed:", updatedTree !== privateFileTree);
          console.log("About to call setPrivateFileTree...");
          setPrivateFileTree(updatedTree);
          console.log("setPrivateFileTree called successfully");
        }
      } else {
        console.log("Cross-library move detected");
        // Cross-library move (Private → Public)
        const nodeToMove = findNodeById(privateFileTree, itemId);
        if (!nodeToMove) {
          console.error("Node not found in source library");
          return;
        }

        console.log("Node to move:", nodeToMove.name);

        // Remove from private library
        const updatedPrivateTree = removeNodeFromTree(privateFileTree, itemId);
        console.log(
          "Updated private tree length after removal:",
          updatedPrivateTree.length,
        );
        console.log(
          "Private tree changed:",
          updatedPrivateTree !== privateFileTree,
        );
        console.log("About to call setPrivateFileTree (removal)...");
        setPrivateFileTree(updatedPrivateTree);
        console.log("setPrivateFileTree called successfully (removal)");

        // Add to public library with new parent
        const nodeWithNewParent = { ...nodeToMove, parentId: newParentId };
        const updatedPublicTree = addNodeToTree(
          publicFileTree,
          nodeWithNewParent,
          newParentId,
        );
        console.log(
          "Updated public tree length after addition:",
          updatedPublicTree.length,
        );
        console.log(
          "Public tree changed:",
          updatedPublicTree !== publicFileTree,
        );
        console.log("About to call setPublicFileTree (addition)...");
        setPublicFileTree(updatedPublicTree);
        console.log("setPublicFileTree called successfully (addition)");
      }
      console.log("=== PERFORM MOVE COMPLETE ===");
    },
    [publicFileTree, privateFileTree, setPublicFileTree, setPrivateFileTree],
  );

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;
      console.log("=== DRAG END DEBUG ===");
      console.log("Drag ended:", { activeId: active.id, overId: over?.id });
      console.log("Current dragSource state:", dragSource);
      setActiveId(null);

      if (!over || active.id === over.id) {
        console.log("No valid drop target");
        setDragSource(null);
        return;
      }

      const draggedId = active.id as string;
      const targetId = over.id as string;

      console.log("Dragged item ID:", draggedId);
      console.log("Target ID:", targetId);

      if (
        targetId === "public-sidebar-root" ||
        targetId === "private-sidebar-root"
      ) {
        const sourceLibrary: LibraryTypeE = findNodeById(
          publicFileTree,
          draggedId,
        )
          ? "public"
          : "private";
        const targetLibrary: LibraryTypeE =
          targetId === "public-sidebar-root" ? "public" : "private";

        if (sourceLibrary === "public" && targetLibrary === "private") {
          setInvalidDropDialog({
            isOpen: true,
            message:
              "You cannot move items from Public library to Private library. Only Private → Public is allowed.",
          });
          setDragSource(null);
          return;
        }

        // Perform the move operation for root drops
        console.log("About to call performMove for root drop");
        performMove(draggedId, null, sourceLibrary, targetLibrary);
        console.log("performMove completed for root drop");
        onItemMove?.(draggedId, null, sourceLibrary, targetLibrary);
        setDragSource(null);
        return;
      }

      const actualTargetId = targetId.startsWith("drop-")
        ? targetId.slice(5)
        : targetId;

      console.log("Actual target ID:", actualTargetId);

      const draggedFromPublic = findNodeById(publicFileTree, draggedId);
      const draggedFromPrivate = findNodeById(privateFileTree, draggedId);
      const targetInPublic = findNodeById(publicFileTree, actualTargetId);
      const targetInPrivate = findNodeById(privateFileTree, actualTargetId);

      console.log("Source detection:");
      console.log(
        "- draggedFromPublic:",
        !!draggedFromPublic,
        draggedFromPublic?.name,
      );
      console.log(
        "- draggedFromPrivate:",
        !!draggedFromPrivate,
        draggedFromPrivate?.name,
      );
      console.log("- targetInPublic:", !!targetInPublic, targetInPublic?.name);
      console.log(
        "- targetInPrivate:",
        !!targetInPrivate,
        targetInPrivate?.name,
      );

      // Use the dragSource state that was set in handleDragStart for more reliable source detection
      const sourceLibrary: LibraryTypeE =
        dragSource || (draggedFromPublic ? "public" : "private");
      const targetLibrary: LibraryTypeE = targetInPublic ? "public" : "private";

      console.log("Detected libraries:");
      console.log("- sourceLibrary (from dragSource):", sourceLibrary);
      console.log("- targetLibrary:", targetLibrary);
      console.log("- dragSource state:", dragSource);

      if (sourceLibrary === "public" && targetLibrary === "private") {
        setInvalidDropDialog({
          isOpen: true,
          message:
            "You cannot move items from Public library to Private library. Only Private → Public is allowed.",
        });
        setDragSource(null);
        return;
      }

      const targetNode = targetInPublic || targetInPrivate;
      if (!targetNode || targetNode.type !== "folder") {
        setDragSource(null);
        return;
      }

      // Perform the move operation for folder drops
      console.log("About to call performMove for folder drop");
      performMove(draggedId, actualTargetId, sourceLibrary, targetLibrary);
      console.log("performMove completed for folder drop");

      // Call the onItemMove callback
      onItemMove?.(draggedId, actualTargetId, sourceLibrary, targetLibrary);

      setDragSource(null);
    },
    [publicFileTree, privateFileTree, onItemMove, performMove],
  );

  const handlePublicItemSelect = useCallback((item: TreeNodeI | null) => {
    setPublicSelectedItem(item?.id || null);
    setPrivateSelectedItem(null);
  }, []);

  const handlePrivateItemSelect = useCallback((item: TreeNodeI | null) => {
    setPrivateSelectedItem(item?.id || null);
    setPublicSelectedItem(null);
  }, []);

  const handlePublicItemMove = useCallback(
    (itemId: string, newParentId: string | null) => {
      performMove(itemId, newParentId, "public", "public");
      onItemMove?.(itemId, newParentId, "public", "public");
    },
    [onItemMove, performMove],
  );

  const handlePrivateItemMove = useCallback(
    (itemId: string, newParentId: string | null) => {
      performMove(itemId, newParentId, "private", "private");
      onItemMove?.(itemId, newParentId, "private", "private");
    },
    [onItemMove, performMove],
  );

  const handlePublicItemCreate = useCallback(
    (item: {
      type: "file" | "folder";
      name: string;
      parentId: string | null;
    }) => {
      onItemCreate?.(item, "public");
    },
    [onItemCreate],
  );

  const handlePrivateItemCreate = useCallback(
    (item: {
      type: "file" | "folder";
      name: string;
      parentId: string | null;
    }) => {
      onItemCreate?.(item, "private");
    },
    [onItemCreate],
  );

  const handlePublicItemRename = useCallback(
    (itemId: string, newName: string) => {
      onItemRename?.(itemId, newName, "public");
    },
    [onItemRename],
  );

  const handlePrivateItemRename = useCallback(
    (itemId: string, newName: string) => {
      onItemRename?.(itemId, newName, "private");
    },
    [onItemRename],
  );

  const handlePublicItemDelete = useCallback(
    (itemId: string) => {
      onItemDelete?.(itemId, "public");
    },
    [onItemDelete],
  );

  const handlePrivateItemDelete = useCallback(
    (itemId: string) => {
      onItemDelete?.(itemId, "private");
    },
    [onItemDelete],
  );

  // Droppable components for sidebar containers
  const PublicSidebarDroppable: React.FC<{ children: React.ReactNode }> = ({
    children,
  }) => {
    const { setNodeRef } = useDroppable({
      id: "public-sidebar-root",
      data: {
        type: "sidebar",
        library: "public",
      },
    });

    return (
      <div ref={setNodeRef} className="h-full">
        {children}
      </div>
    );
  };

  const PrivateSidebarDroppable: React.FC<{ children: React.ReactNode }> = ({
    children,
  }) => {
    const { setNodeRef } = useDroppable({
      id: "private-sidebar-root",
      data: {
        type: "sidebar",
        library: "private",
      },
    });

    return (
      <div ref={setNodeRef} className="h-full">
        {children}
      </div>
    );
  };

  return (
    <>
      {/* Info Banner */}
      <div className="bg-blue-50 border-b border-blue-200 px-4 py-[22px]">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <svg
              className="w-4 h-4 text-blue-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
            <p className="text-xs text-blue-800">
              <span className="font-semibold">Split View Mode:</span> Drag
              files/folders from Private (right) to Public (left) only.
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-red-700 hover:text-blue-800 text-xs font-medium"
          >
            Exit
          </button>
        </div>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart as () => void}
        onDragEnd={handleDragEnd}
      >
        <div className="flex h-full min-h-screen">
          {/* Left: Public Library Sidebar - Full width (w-64) */}
          <div className="w-64 flex-shrink-0 border-r-2 border-neutral-300 relative">
            {dragSource === "private" && (
              <div className="absolute inset-0 bg-green-50 bg-opacity-30 pointer-events-none z-10 border-2 border-green-400 border-dashed">
                <div className="flex items-center justify-center h-full">
                  <div className="bg-green-100 px-4 py-2 rounded-lg shadow-lg">
                    <p className="text-sm font-medium text-green-800">
                      Drop here to move to Public
                    </p>
                  </div>
                </div>
              </div>
            )}
            <PublicSidebarDroppable>
              <FolderSidebar
                fileTree={publicFileTree}
                selectedItem={publicSelectedItem}
                onItemSelect={handlePublicItemSelect}
                onItemMove={handlePublicItemMove}
                onItemCreate={handlePublicItemCreate}
                onItemRename={handlePublicItemRename}
                onItemDelete={handlePublicItemDelete}
                enableDragDrop={true} // Enable drag-drop but disable internal context
                disableInternalDndContext={true} // Use parent DndContext
                maxDepth={10}
                libraryType="public"
                isSplitView={true}
              />
            </PublicSidebarDroppable>
          </div>

          {/* Right: Private Library Sidebar - Full width (w-64) */}
          <div className="w-64 flex-shrink-0 relative">
            {dragSource === "public" && (
              <div className="absolute inset-0 bg-red-50 bg-opacity-30 pointer-events-none z-10 border-2 border-red-400 border-dashed">
                <div className="flex items-center justify-center h-full">
                  <div className="bg-red-100 px-4 py-2 rounded-lg shadow-lg">
                    <p className="text-sm font-medium text-red-800">
                      Cannot drop here
                    </p>
                  </div>
                </div>
              </div>
            )}
            <PrivateSidebarDroppable>
              <FolderSidebar
                fileTree={privateFileTree}
                selectedItem={privateSelectedItem}
                onItemSelect={handlePrivateItemSelect}
                onItemMove={handlePrivateItemMove}
                onItemCreate={handlePrivateItemCreate}
                onItemRename={handlePrivateItemRename}
                onItemDelete={handlePrivateItemDelete}
                enableDragDrop={true} // Enable drag from private
                disableInternalDndContext={true} // Use parent DndContext
                maxDepth={10}
                libraryType="private"
                isSplitView={true}
              />
            </PrivateSidebarDroppable>
          </div>
        </div>

        {/* Drag Overlay for visual feedback */}
        <DragOverlay>
          {activeId ? (
            <div className="bg-white border-2 border-primary-500 rounded px-3 py-2 shadow-xl">
              <span className="text-sm font-medium">
                {findNodeById(publicFileTree, activeId)?.name ||
                  findNodeById(privateFileTree, activeId)?.name}
              </span>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* Invalid Drop Dialog */}
      <ConfirmDialog
        isOpen={invalidDropDialog.isOpen}
        title="Invalid Operation"
        message={invalidDropDialog.message}
        confirmText="OK"
        variant="danger"
        onConfirm={() =>
          setInvalidDropDialog({
            isOpen: false,
            message: "",
          })
        }
        onCancel={() =>
          setInvalidDropDialog({
            isOpen: false,
            message: "",
          })
        }
      />
    </>
  );
};

export default SplitViewSidebar;
