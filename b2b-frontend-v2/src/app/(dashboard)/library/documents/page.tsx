"use client";
import DashboardHeader from "@/components/DashboardHeader";
import DocumentGrid from "@/components/DocumentGrid";
import StickyFooter from "@/components/StickyFooter";
import React, { useState, useEffect } from "react";
import { DocumentI } from "@/components/DocumentCard/types";
import { useLibrary } from "../components/LibraryLayoutClient/context";

const LibraryPage = () => {
  const { setFolders, setSelectedFolder } = useLibrary();
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null);

  // Initialize data when component mounts
  useEffect(() => {
    const folders = [
      {
        id: "1",
        name: "Folder 1",
        type: "folder" as const,
        path: "/Folder 1",
        parentId: null,
        children: [],
        isExpanded: false,
        count: 5,
      },
      {
        id: "2",
        name: "Folder 2",
        type: "folder" as const,
        path: "/Folder 2",
        parentId: null,
        children: [],
        isExpanded: false,
        count: 3,
      },
      {
        id: "3",
        name: "Folder 3",
        type: "folder" as const,
        path: "/Folder 3",
        parentId: null,
        children: [],
        isExpanded: false,
        count: 8,
      },
      {
        id: "4",
        name: "Folder 4",
        type: "folder" as const,
        path: "/Folder 4",
        parentId: null,
        children: [],
        isExpanded: false,
        count: 2,
      },
      {
        id: "5",
        name: "Folder 5",
        type: "folder" as const,
        path: "/Folder 5",
        parentId: null,
        children: [],
        isExpanded: false,
        count: 12,
      },
      {
        id: "6",
        name: "Folder 6",
        type: "folder" as const,
        path: "/Folder 6",
        parentId: null,
        children: [],
        isExpanded: false,
        count: 7,
      },
    ];

    setFolders(folders);
    setSelectedFolder("Folder 1");
  }, [setFolders, setSelectedFolder]);

  const documents = [
    { id: "1", name: "Document Name 1", type: "word", folderId: "1" },
    { id: "2", name: "Document Name 1", type: "pdf", folderId: "1" },
    { id: "3", name: "Document Name 1", type: "pdf", folderId: "1" },
    { id: "4", name: "Document Name 1", type: "pdf", folderId: "1" },
    { id: "5", name: "Document Name 1", type: "word", folderId: "1" },
    { id: "6", name: "Document Name 2", type: "word", folderId: "2" },
    { id: "7", name: "Document Name 3", type: "pdf", folderId: "3" },
  ];

  const handleDocumentSelect = (document: DocumentI) => {
    if (selectedDocument === document.id) {
      setSelectedDocument(null);
    } else {
      setSelectedDocument(document.id);
    }
  };

  const documentCount = documents.length;
  const selectedDocumentInfo = selectedDocument
    ? documents.find(doc => doc.id === selectedDocument)
    : null;

  const footerLeftItems = [
    <div key="doc-count" className="text-white hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors">
      📄 {documentCount} document{documentCount !== 1 ? 's' : ''}
    </div>,
    <div key="library-type" className="text-white hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors">
      📚 Public Library
    </div>
  ];

  const footerCenterItems = selectedDocumentInfo ? [
    <div key="selected-doc" className="text-white hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors">
      📋 Selected: {selectedDocumentInfo.name} ({selectedDocumentInfo.type.toUpperCase()})
    </div>
  ] : [
    <div key="no-selection" className="text-blue-200">
      No document selected
    </div>
  ];

  const footerRightItems = [
    <div key="sync-status" className="text-white hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors">
      🔄 Synced
    </div>,
    <div key="last-modified" className="text-white hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors">
      🕒 {new Date().toLocaleDateString()}
    </div>
  ];

  return (
    <div className="flex flex-1 flex-col h-full relative">
      <DashboardHeader
        title="Documents"
        description="Manage and organize your document collection"
        actions={[
          {
            label: "Edit Document",
            variant: "ghost",
            className:
              "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto",
            onClick: () => console.log("Edit Document"),
          },
          {
            label: "Upload Document",
            variant: "primary",
            className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
            onClick: () => console.log("Upload Document"),
          },
        ]}
      />

      <div className="flex-1 p-6 bg-neutral-50 pb-12">
        <DocumentGrid
          documents={documents}
          selectedDocument={selectedDocument}
          onDocumentSelect={handleDocumentSelect}
        />
      </div>

      <StickyFooter
        showVersion={true}
        version="2.1.4"
        leftItems={footerLeftItems}
        centerItems={footerCenterItems}
        rightItems={footerRightItems}
      />
    </div>
  );
};

export default LibraryPage;
