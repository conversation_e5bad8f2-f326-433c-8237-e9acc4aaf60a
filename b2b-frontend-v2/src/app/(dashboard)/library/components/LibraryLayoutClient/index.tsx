"use client";

import React, { useState } from "react";
import FolderSidebar from "../../documents/components/FolderSidebar";
import StickyFooter from "@/components/StickyFooter";
import { useSidebar } from "@/contexts/SidebarContext";
import { LibraryProvider, useLibrary } from "./context";

const LibraryLayoutContent: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const {
    fileTree,
    selectedItem,
    setSelectedItem,
    folders,
    selectedFolder,
    setSelectedFolder,
    setSidebarExpanded,
    onItemMove,
    onItemCreate,
    onItemRename,
    onItemDelete,
  } = useLibrary();

  const { sidebarCollapsed } = useSidebar();
  const [isSplitViewActive, setIsSplitViewActive] = useState(false);

  const handleVersionChange = (version: string) => {
    console.log('Version changed to:', version);
    // Handle version change logic here
  };

  return (
    <div className="flex h-full min-h-screen overflow-hidden relative">
      <div
        className={`flex-shrink-0 h-full min-h-screen transition-all duration-300 ${
          isSplitViewActive ? "w-128" : "w-64"
        }`}
      >
        <FolderSidebar
          fileTree={fileTree}
          selectedItem={selectedItem}
          onItemSelect={(item) => setSelectedItem(item?.id || null)}
          onItemMove={onItemMove}
          onItemCreate={onItemCreate}
          onItemRename={onItemRename}
          onItemDelete={onItemDelete}
          folders={folders}
          selectedFolder={selectedFolder}
          onFolderSelect={setSelectedFolder}
          enableDragDrop={true}
          maxDepth={10}
          onLibraryToggle={setSidebarExpanded}
          onSplitViewChange={setIsSplitViewActive}
          onAddFile={() => console.log("Add file")}
          onAddFolder={() => console.log("Add folder")}
        />
      </div>

      <div className="flex-1 h-full min-h-screen overflow-auto pb-8">{children}</div>

      <StickyFooter
        version="2.1.4"
        sidebarCollapsed={sidebarCollapsed}
        onVersionChange={handleVersionChange}
      />
    </div>
  );
};

const LibraryLayoutClient: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <LibraryProvider>
      <LibraryLayoutContent>{children}</LibraryLayoutContent>
    </LibraryProvider>
  );
};

export default LibraryLayoutClient;
