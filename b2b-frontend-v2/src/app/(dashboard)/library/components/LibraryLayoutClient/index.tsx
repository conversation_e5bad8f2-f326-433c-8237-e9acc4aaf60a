"use client";

import React, { useState } from "react";
import FolderSidebar from "../../documents/components/FolderSidebar";
import StickyFooter from "@/components/StickyFooter";
import { LibraryProvider, useLibrary } from "./context";

const LibraryLayoutContent: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const {
    fileTree,
    selectedItem,
    setSelectedItem,
    folders,
    selectedFolder,
    setSelectedFolder,
    setSidebarExpanded,
    onItemMove,
    onItemCreate,
    onItemRename,
    onItemDelete,
  } = useLibrary();

  const [isSplitViewActive, setIsSplitViewActive] = useState(false);

  const getLibraryFooterItems = () => {
    const totalFiles = fileTree.length;
    const totalFolders = folders.length;
    const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
    const currentPage = currentPath.split('/').pop() || 'library';

    const leftItems = [
      <div key="workspace" className="flex items-center gap-1 hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors text-xs">
        <span className="text-blue-200">📚</span>
        <span>Library</span>
      </div>,
      <div key="files-count" className="flex items-center gap-1 hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors text-xs">
        <span className="text-blue-200">📄</span>
        <span>{totalFiles} files</span>
      </div>,
      <div key="folders-count" className="flex items-center gap-1 hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors text-xs">
        <span className="text-blue-200">📁</span>
        <span>{totalFolders} folders</span>
      </div>
    ];

    const centerItems = selectedItem ? [
      <div key="selected-item" className="flex items-center gap-1 hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors text-xs">
        <span className="text-blue-200">📋</span>
        <span>Selected: {selectedItem}</span>
      </div>
    ] : [
      <div key="current-page" className="flex items-center gap-1 text-blue-200 text-xs">
        <span>📍</span>
        <span className="capitalize">{currentPage.replace('-', ' ')}</span>
      </div>
    ];

    const rightItems = [
      <div key="sync-status" className="flex items-center gap-1 hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors text-xs">
        <span className="text-green-300">●</span>
        <span>Synced</span>
      </div>,
      <div key="encoding" className="flex items-center gap-1 hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors text-xs">
        <span className="text-blue-200">UTF-8</span>
      </div>,
      <div key="timestamp" className="flex items-center gap-1 hover:bg-blue-500 px-2 py-1 rounded cursor-pointer transition-colors text-xs">
        <span className="text-blue-200">🕒</span>
        <span>{new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
      </div>
    ];

    return { leftItems, centerItems, rightItems };
  };

  const { leftItems, centerItems, rightItems } = getLibraryFooterItems();

  return (
    <div className="flex h-full min-h-screen overflow-hidden relative">
      <div
        className={`flex-shrink-0 h-full min-h-screen transition-all duration-300 ${
          isSplitViewActive ? "w-128" : "w-64"
        }`}
      >
        <FolderSidebar
          fileTree={fileTree}
          selectedItem={selectedItem}
          onItemSelect={(item) => setSelectedItem(item?.id || null)}
          onItemMove={onItemMove}
          onItemCreate={onItemCreate}
          onItemRename={onItemRename}
          onItemDelete={onItemDelete}
          folders={folders}
          selectedFolder={selectedFolder}
          onFolderSelect={setSelectedFolder}
          enableDragDrop={true}
          maxDepth={10}
          onLibraryToggle={setSidebarExpanded}
          onSplitViewChange={setIsSplitViewActive}
          onAddFile={() => console.log("Add file")}
          onAddFolder={() => console.log("Add folder")}
        />
      </div>

      <div className="flex-1 h-full min-h-screen overflow-auto pb-8">{children}</div>

      <StickyFooter
        version="2.1.4"
        leftItems={leftItems}
        centerItems={centerItems}
        rightItems={rightItems}
      />
    </div>
  );
};

const LibraryLayoutClient: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <LibraryProvider>
      <LibraryLayoutContent>{children}</LibraryLayoutContent>
    </LibraryProvider>
  );
};

export default LibraryLayoutClient;
